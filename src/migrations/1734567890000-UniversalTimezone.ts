import { MigrationInterface, QueryRunner } from 'typeorm';

export class UniversalTimezone1734567890000 implements MigrationInterface {
  name = 'UniversalTimezone1734567890000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // ADD TIMEZONE COLUMN TO DOCTOR TABLE
    await queryRunner.query(
      `ALTER TABLE "doctor" ADD "timezone" character varying`,
    );

    // MIGRATE EXISTING TIMEZONE DATA FROM WEEKLY SCHEDULES TO DOCTORS
    // GET FIRST NON-NULL TIMEZONE FOR EACH DOCTOR FROM WEEKLY SCHEDULES
    await queryRunner.query(`
      UPDATE "doctor" 
      SET "timezone" = (
        SELECT "timezone" 
        FROM "weekly_schedule" 
        WHERE "weekly_schedule"."doctorId" = "doctor"."id" 
        AND "weekly_schedule"."timezone" IS NOT NULL 
        LIMIT 1
      )
      WHERE EXISTS (
        SELECT 1 
        FROM "weekly_schedule" 
        WHERE "weekly_schedule"."doctorId" = "doctor"."id" 
        AND "weekly_schedule"."timezone" IS NOT NULL
      )
    `);

    // MIGRATE EXISTING TIMEZONE DATA FROM OVERRIDE SCHEDULES TO DOCTORS (IF NOT ALREADY SET)
    await queryRunner.query(`
      UPDATE "doctor" 
      SET "timezone" = (
        SELECT "timezone" 
        FROM "override_schedule" 
        WHERE "override_schedule"."doctorId" = "doctor"."id" 
        AND "override_schedule"."timezone" IS NOT NULL 
        LIMIT 1
      )
      WHERE "timezone" IS NULL 
      AND EXISTS (
        SELECT 1 
        FROM "override_schedule" 
        WHERE "override_schedule"."doctorId" = "doctor"."id" 
        AND "override_schedule"."timezone" IS NOT NULL
      )
    `);

    // REMOVE TIMEZONE COLUMN FROM WEEKLY_SCHEDULE TABLE
    await queryRunner.query(
      `ALTER TABLE "weekly_schedule" DROP COLUMN "timezone"`,
    );

    // REMOVE TIMEZONE COLUMN FROM OVERRIDE_SCHEDULE TABLE
    await queryRunner.query(
      `ALTER TABLE "override_schedule" DROP COLUMN "timezone"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // ADD TIMEZONE COLUMN BACK TO WEEKLY_SCHEDULE TABLE
    await queryRunner.query(
      `ALTER TABLE "weekly_schedule" ADD "timezone" character varying`,
    );

    // ADD TIMEZONE COLUMN BACK TO OVERRIDE_SCHEDULE TABLE
    await queryRunner.query(
      `ALTER TABLE "override_schedule" ADD "timezone" character varying`,
    );

    // MIGRATE TIMEZONE DATA BACK FROM DOCTORS TO WEEKLY SCHEDULES
    await queryRunner.query(`
      UPDATE "weekly_schedule" 
      SET "timezone" = (
        SELECT "timezone" 
        FROM "doctor" 
        WHERE "doctor"."id" = "weekly_schedule"."doctorId"
      )
      WHERE EXISTS (
        SELECT 1 
        FROM "doctor" 
        WHERE "doctor"."id" = "weekly_schedule"."doctorId" 
        AND "doctor"."timezone" IS NOT NULL
      )
    `);

    // MIGRATE TIMEZONE DATA BACK FROM DOCTORS TO OVERRIDE SCHEDULES
    await queryRunner.query(`
      UPDATE "override_schedule" 
      SET "timezone" = (
        SELECT "timezone" 
        FROM "doctor" 
        WHERE "doctor"."id" = "override_schedule"."doctorId"
      )
      WHERE EXISTS (
        SELECT 1 
        FROM "doctor" 
        WHERE "doctor"."id" = "override_schedule"."doctorId" 
        AND "doctor"."timezone" IS NOT NULL
      )
    `);

    // REMOVE TIMEZONE COLUMN FROM DOCTOR TABLE
    await queryRunner.query(`ALTER TABLE "doctor" DROP COLUMN "timezone"`);
  }
}
