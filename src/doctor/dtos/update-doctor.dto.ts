// src/doctor/dto/update-doctor.dto.ts

import {
  IsEmail,
  IsOptional,
  IsString,
  IsEnum,
  IsDate,
  IsArray,
  IsNumber,
  IsTimeZone,
} from 'class-validator';
import { Gender } from '../entities/doctor.entity';
import { Transform, Type } from 'class-transformer';

export class UpdateQualificationDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsOptional()
  @IsString()
  institutionName?: string;

  @IsOptional()
  @IsString()
  degreeName?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;

  // This property can be used if a URL is provided instead of a file.
  @IsOptional()
  @IsString()
  degreeFileUrl?: string;
}

export class UpdateAwardDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsOptional()
  @IsString()
  awardName?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  issueDate?: Date;

  @IsOptional()
  @IsString()
  awardedBy?: string;

  @IsOptional()
  @IsString()
  awardDetails?: string;
}

export class UpdatePracticeExperienceDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsOptional()
  @IsString()
  hospitalName?: string;

  @IsOptional()
  @IsString()
  designation?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;

  // For file update – either a URL or a file upload.
  @IsOptional()
  @IsString()
  experienceFileUrl?: string;
}

export class UpdateDoctorDto {
  @IsOptional()
  @IsString()
  doctorId?: string;

  @IsOptional()
  @IsEmail()
  @Transform(({ value }) => value.toLowerCase())
  email?: string;

  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsString()
  contactNumber?: string;

  @IsOptional()
  @IsEnum(Gender)
  gender?: Gender;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  dob?: Date;

  @IsOptional()
  @IsString()
  address?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  language?: string[];

  @IsOptional()
  @IsString()
  country?: string;

  @IsOptional()
  @IsString()
  bio?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  speciality?: string[];

  @IsOptional()
  @IsNumber()
  yearsOfExperience?: number;

  @IsOptional()
  @IsString()
  medicalLicenseNumber?: string;

  @IsOptional()
  @IsString()
  membershipDetails?: string;

  @IsOptional()
  @IsString()
  @IsTimeZone()
  timezone?: string; // e.g., "America/New_York", "Asia/Karachi"

  @IsOptional()
  @IsString()
  profilePictureUrl?: string;

  @IsOptional()
  @IsString()
  signatureUrl?: string;

  @IsOptional()
  @IsString()
  idFrontUrl?: string;

  @IsOptional()
  @IsString()
  idBackUrl?: string;

  // Nested arrays
  @IsOptional()
  @IsArray()
  qualifications?: UpdateQualificationDto[];

  @IsOptional()
  @IsArray()
  awards?: UpdateAwardDto[];

  @IsOptional()
  @IsArray()
  practiceExperiences?: UpdatePracticeExperienceDto[];
}
