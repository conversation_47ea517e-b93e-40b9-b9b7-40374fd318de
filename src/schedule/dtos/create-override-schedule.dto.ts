import {
  IsOptional,
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsDateString,
  IsInt,
  Min,
  IsDate,
  ArrayMinSize,
  IsMilitaryTime,
  IsTimeZone,
} from 'class-validator';
import { Type } from 'class-transformer';

// DTO for a single override session
export class OverrideSessionDto {
  @IsNotEmpty()
  @IsMilitaryTime()
  @IsString()
  startTime: string; // e.g. "10:00:00"

  @IsNotEmpty()
  @IsMilitaryTime()
  @IsString()
  endTime: string; // e.g. "14:00:00"

  @IsInt()
  @Min(10)
  slotDuration: number; // e.g. 30
}

//  DTO for creating a single date's override schedule

export class CreateOverrideScheduleDto {
  @IsOptional()
  @IsString()
  doctorId?: string;

  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  date: Date;

  @IsOptional()
  @IsString()
  @IsTimeZone()
  timezone?: string; // e.g., "America/New_York", "Asia/Karachi"

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => OverrideSessionDto)
  sessions: OverrideSessionDto[];
}
