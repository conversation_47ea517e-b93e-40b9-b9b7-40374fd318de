import {
  IsOptional,
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsDate,
  ArrayMinSize,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OverrideSessionDto } from './create-override-schedule.dto';

class MultipleOverrideItem {
  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  date: Date;

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => OverrideSessionDto)
  sessions: OverrideSessionDto[];
}

export class CreateMultipleOverrideScheduleDto {
  @IsOptional()
  @IsString()
  doctorId?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MultipleOverrideItem)
  overrideSchedules: MultipleOverrideItem[];
}
