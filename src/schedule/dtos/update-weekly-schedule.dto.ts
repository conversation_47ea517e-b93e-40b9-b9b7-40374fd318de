import {
  IsOptional,
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsEnum,
  ArrayMinSize,
  IsTimeZone,
} from 'class-validator';
import { Type } from 'class-transformer';
import { DayOfWeek } from '../entities/weeklySchedule.entity';
import { WeeklySessionDto } from './create-weekly-schedule.dto';

class UpdateWeeklyItem {
  @IsNotEmpty()
  @IsEnum(DayOfWeek)
  dayOfWeek: DayOfWeek;

  @IsOptional()
  @IsString()
  @IsTimeZone()
  timezone?: string; // e.g., "America/New_York", "Asia/Karachi"

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => WeeklySessionDto)
  sessions: WeeklySessionDto[];
}

export class UpdateWeeklyScheduleDto {
  @IsOptional()
  @IsString()
  doctorId?: string;

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => UpdateWeeklyItem)
  schedules: UpdateWeeklyItem[];
}
