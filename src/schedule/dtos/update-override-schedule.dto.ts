import {
  IsOptional,
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsDateString,
  IsDate,
  ArrayMinSize,
  IsTimeZone,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OverrideSessionDto } from './create-override-schedule.dto';

class UpdateOverrideItem {
  @IsNotEmpty()
  @Type(() => Date)
  @IsDate()
  date: Date;

  @IsOptional()
  @IsString()
  @IsTimeZone()
  timezone?: string; // e.g., "America/New_York", "Asia/Karachi"

  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => OverrideSessionDto)
  sessions: OverrideSessionDto[];
}

export class UpdateOverrideScheduleDto {
  @IsOptional()
  @IsString()
  doctorId?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateOverrideItem)
  overrideSchedules: UpdateOverrideItem[];
}
