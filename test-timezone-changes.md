# Timezone Changes Test Guide

## Overview
The timezone has been moved from individual schedule entities to the doctor entity to make it universal and prevent overlapping issues.

## Changes Made

### 1. Database Schema Changes
- **Added** `timezone` field to `Doctor` entity
- **Removed** `timezone` field from `WeeklySchedule` entity  
- **Removed** `timezone` field from `OverrideSchedule` entity

### 2. DTO Changes
- **Added** `timezone` field to `CreateDoctorDto` and `UpdateDoctorDto`
- **Removed** `timezone` field from all schedule DTOs:
  - `CreateWeeklyScheduleDto`
  - `CreateMultipleWeeklyScheduleDto` 
  - `UpdateWeeklyScheduleDto`
  - `CreateOverrideScheduleDto`
  - `CreateMultipleOverrideScheduleDto`
  - `UpdateOverrideScheduleDto`

### 3. Service Logic Changes
- Updated `ScheduleService.getDoctorSlotDetails()` to use `doctor.timezone` instead of `schedule.timezone`
- Removed timezone update logic from schedule update methods
- All slot generation now uses the doctor's universal timezone

### 4. Migration
- Created migration `UniversalTimezone1734567890000` to:
  - Add timezone column to doctor table
  - Migrate existing timezone data from schedules to doctors
  - Remove timezone columns from schedule tables

## Testing Steps

### 1. Run Migration
```bash
npm run migration:run
```

### 2. Test Doctor Creation with Timezone
```bash
curl -X POST http://localhost:3000/api/doctor/signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "Doctor",
    "timezone": "America/New_York"
  }'
```

### 3. Test Doctor Update with Timezone
```bash
curl -X PUT http://localhost:3000/api/doctor/update \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "doctorData": "{\"timezone\": \"Asia/Karachi\"}"
  }'
```

### 4. Test Schedule Creation (No Timezone Required)
```bash
curl -X POST http://localhost:3000/api/schedule/createWeeklySingleDay \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "dayOfWeek": "monday",
    "sessions": [{
      "startTime": "09:00",
      "endTime": "17:00", 
      "slotDuration": 30
    }]
  }'
```

### 5. Test Slot Fetching (Uses Doctor's Timezone)
```bash
curl -X GET "http://localhost:3000/api/schedule/getDoctorSlotDetails/DOCTOR_ID?startDate=2024-01-01&endDate=2024-01-07&requestedTimezone=America/New_York" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Expected Behavior

### Before Changes (Problems)
- Each weekly schedule could have different timezones
- Override schedules could have different timezones than weekly schedules
- Slot calculations were complex and error-prone
- Timezone conflicts caused overlapping issues

### After Changes (Solutions)
- Single universal timezone per doctor
- All schedules for a doctor use the same timezone
- Simplified slot calculations
- No more timezone conflicts or overlapping issues
- Consistent timezone handling across all APIs

## Verification Points

1. **Doctor Profile**: Verify timezone is saved and returned in doctor profile
2. **Schedule Creation**: Verify schedules can be created without timezone parameter
3. **Slot Generation**: Verify all slots use doctor's timezone for calculations
4. **Timezone Conversion**: Verify slots are correctly converted to requested timezone
5. **No Overlaps**: Verify no overlapping issues occur with universal timezone

## Rollback Plan

If issues occur, run the down migration:
```bash
npm run migration:revert
```

This will:
- Remove timezone from doctor table
- Add timezone back to schedule tables
- Migrate timezone data back to schedules
